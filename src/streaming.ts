import { DBOS } from '@dbos-inc/dbos-sdk';
import { ForaChat } from './operations';
import { DelayedMessage, QueuedMessage } from './models/types';
import { ChatService } from './core/ChatService';
import { ConversationService } from './core/ConversationService';
import { MessageQueueService } from './core/MessageQueueService';
import { PromptService } from './core/PromptService';
import WebSocket from 'ws';

interface ChatResponse {
  reply: DelayedMessage[];
  theme?: string;
  skills?: string[];
  error?: string;
  details?: string;
  conversationId?: number;
}

interface StreamingSession {
  ws: WebSocket;
  isStreaming: boolean;
  timeouts: NodeJS.Timeout[];
  interrupted: boolean;
  pendingMessages: DelayedMessage[];
  conversationId?: number;
  lastMessageId?: number;
  lastUserMessage?: string;
  pollInterval?: NodeJS.Timeout;
  autonomousTimer?: NodeJS.Timeout;
  extendedWorkflowTimer?: NodeJS.Timeout;
  extendedWorkflowActive: boolean;
  extendedWorkflowStartTime?: number;
  extendedWorkflowCharacterTimers: NodeJS.Timeout[];
}

export class StreamingChatService {
  private sessions: Map<string, StreamingSession> = new Map();
  private chatService: ChatService;

  constructor(chatService: ChatService) {
    this.chatService = chatService;
  }

  createSession(sessionId: string, ws: WebSocket): void {
    const session: StreamingSession = {
      ws,
      isStreaming: false,
      timeouts: [],
      interrupted: false,
      pendingMessages: [],
      extendedWorkflowActive: false,
      extendedWorkflowCharacterTimers: []
    };

    this.sessions.set(sessionId, session);

    ws.on('message', async (data) => {
      try {
        const message = JSON.parse(data.toString());
        await this.handleMessage(sessionId, message);
      } catch (error) {
        this.sendError(sessionId, 'Invalid message format');
      }
    });

    ws.on('close', () => {
      this.cleanupSession(sessionId);
    });

    // Send welcome message
    this.sendMessage(sessionId, {
      type: 'connected',
      sessionId
    });
  }

  private async handleMessage(sessionId: string, message: any): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    switch (message.type) {
      case 'chat':
        await this.handleChatMessage(sessionId, message.text);
        break;
      case 'interrupt':
        this.handleInterrupt(sessionId, message.text);
        break;
      case 'start_extended_workflow':
        await this.triggerExtendedWorkflow(sessionId);
        break;
      case 'get_extended_workflow_status':
        const status = this.getExtendedWorkflowStatus(sessionId);
        this.sendMessage(sessionId, {
          type: 'extended_workflow_status',
          ...status
        });
        break;
      default:
        this.sendError(sessionId, 'Unknown message type');
    }
  }

  private async handleChatMessage(sessionId: string, text: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    // If extended workflow is active, extend the timeout due to user activity
    if (session.extendedWorkflowActive) {
      this.extendWorkflowTimeout(sessionId);
    }

    if (session.isStreaming) {
      // User interrupted while messages are being streamed
      this.handleInterrupt(sessionId, text);
      return;
    }

    try {
      DBOS.logger.info(`=== STREAMING CHAT MESSAGE PROCESSING ===`);
      DBOS.logger.info(`Session ID: ${sessionId}`);
      DBOS.logger.info(`User Message: "${text}"`);

      let result;

      // If we have a conversation ID, continue the conversation, otherwise start new
      if (session.conversationId) {
        DBOS.logger.info(`Existing Conversation ID: ${session.conversationId}`);
        DBOS.logger.info(`Continuing existing conversation`);
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text, session.conversationId);
        result = await handle.getResult();
      } else {
        DBOS.logger.info(`No existing conversation - starting new conversation`);
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text);
        result = await handle.getResult();
        session.conversationId = result.conversationId;
      }

      const response = result;

      if (!response) {
        this.sendError(sessionId, 'No response from chat service');
        return;
      }

      if (response.reply && Array.isArray(response.reply)) {

        DBOS.logger.info(`=== CHAT WORKFLOW COMPLETED ===`);
        DBOS.logger.info(`Final Conversation ID: ${response.conversationId}`);
        DBOS.logger.info(`Response Messages: ${response.reply.length}`);
        DBOS.logger.info(`Theme: ${response.theme || 'Not set'}`);
        DBOS.logger.info(`Skills: ${response.skills ? JSON.stringify(response.skills) : 'Not set'}`);
        DBOS.logger.info(`Session will continue with conversation ID: ${session.conversationId}`);
        DBOS.logger.info(`=======================================`);

        this.sendMessage(sessionId, {
          type: 'chat_start',
          theme: response.theme || 'Chat Response'
        });

        await this.streamDelayedMessages(sessionId, response.reply, response.skills);

        // Get the current highest message ID to avoid re-polling initial messages
        const allMessages = await ConversationService.getDelayedThoughts(session.conversationId!);
        if (allMessages.length > 0) {
          session.lastMessageId = Math.max(...allMessages.map(m => m.id || 0));
          DBOS.logger.info(`Set lastMessageId to ${session.lastMessageId} to avoid re-polling initial messages`);
        }

        // Start polling for delayed character thoughts
        this.pollForDelayedThoughts(sessionId);

        // Schedule autonomous character interactions if user is inactive
        this.scheduleAutonomousInteraction(sessionId);
      } else {
        this.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.sendError(sessionId, 'Failed to process chat message', (error as Error).message);
    }
  }

  private async handleInterrupt(sessionId: string, newText: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const wasExtendedWorkflowActive = session.extendedWorkflowActive;

    session.interrupted = true;
    session.lastUserMessage = newText; // Track the user's latest message
    this.clearTimeouts(sessionId);

    // Cancel any pending character thoughts that haven't been processed yet
    await this.cancelPendingCharacterThoughts(sessionId);

    // Send appropriate interruption message
    const interruptMessage = wasExtendedWorkflowActive
      ? 'Extended conversation interrupted. Processing your new message...'
      : 'Processing your new message...';

    this.sendMessage(sessionId, {
      type: 'interrupted',
      message: interruptMessage,
      extendedWorkflowInterrupted: wasExtendedWorkflowActive
    });

    // Get the messages that were already sent
    const sentMessages: DelayedMessage[] = [];

    // Calculate which messages were already sent based on timing
    let cumulativeDelay = 0;

    for (const message of session.pendingMessages) {
      cumulativeDelay += (message.delay || 0);
      // If enough time has passed for this message to be sent, include it
      if (cumulativeDelay <= 1000) { // Rough estimate - in real implementation would track start time
        sentMessages.push(message);
      } else {
        break;
      }
    }

    try {
      // Use the interrupted chat workflow with context
      const handle = await DBOS.startWorkflow(ForaChat).interruptedChatWorkflow(
        newText,
        sentMessages.map(msg => ({ character: msg.character, text: msg.text })),
        session.conversationId
      );
      const result: ChatResponse = await handle.getResult();

      if (result.error) {
        this.sendError(sessionId, result.error, result.details);
        return;
      }

      if (result.reply && Array.isArray(result.reply)) {
        this.sendMessage(sessionId, {
          type: 'chat_start',
          theme: result.theme || 'Chat Response (Interrupted)'
        });

        await this.streamDelayedMessages(sessionId, result.reply, result.skills);

        // Get the current highest message ID to avoid re-polling initial messages
        const allMessages = await ConversationService.getDelayedThoughts(session.conversationId!);
        if (allMessages.length > 0) {
          session.lastMessageId = Math.max(...allMessages.map(m => m.id || 0));
          DBOS.logger.info(`Set lastMessageId to ${session.lastMessageId} after interrupt to avoid re-polling initial messages`);
        }

        // Start polling for delayed character thoughts for interrupted messages too
        this.pollForDelayedThoughts(sessionId);
      } else {
        this.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.sendError(sessionId, 'Failed to process interrupted message', (error as Error).message);
    }
  }

  private async streamDelayedMessages(
    sessionId: string,
    messages: DelayedMessage[],
    skills?: string[]
  ): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.isStreaming = true;
    session.interrupted = false;
    session.pendingMessages = [...messages];

    // Handle empty messages case (e.g., general greetings)
    if (messages.length === 0) {
      // Send chat_complete immediately and start extended workflow
      setTimeout(async () => {
        if (!session.interrupted) {
          this.sendMessage(sessionId, {
            type: 'chat_complete',
            skills: skills || []
          });
          session.isStreaming = false;
          session.pendingMessages = [];

          // Start the extended 10-minute workflow
          await this.startExtendedWorkflow(sessionId);
        }
      }, 100);
      return;
    }

    let cumulativeDelay = 0;
    const TYPING_INDICATOR_LEAD_TIME = 3000; // Show typing indicator 3 seconds before message

    messages.forEach((message, index) => {
      const delay = message.delay || 0;
      cumulativeDelay += delay; // Delay is already in milliseconds

      // Schedule typing indicator to show before the message
      // Ensure typing indicator never shows immediately (minimum 1 second delay)
      const typingStartTime = Math.max(1000, cumulativeDelay - TYPING_INDICATOR_LEAD_TIME);
      const typingTimeout = setTimeout(() => {
        if (session.interrupted) {
          return; // Don't show typing if interrupted
        }

        this.sendMessage(sessionId, {
          type: 'typing_start',
          character: message.character
        });
      }, typingStartTime);

      session.timeouts.push(typingTimeout);

      // Schedule the actual message
      const messageTimeout = setTimeout(() => {
        if (session.interrupted) {
          return; // Don't send if interrupted
        }

        // Stop typing indicator just before sending message
        this.sendMessage(sessionId, {
          type: 'typing_stop',
          character: message.character
        });

        this.sendMessage(sessionId, {
          type: 'message',
          character: message.character,
          text: message.text,
          index,
          total: messages.length
        });

        // If this is the last message, finish the stream
        if (index === messages.length - 1) {
          setTimeout(async () => {
            if (!session.interrupted) {
              this.sendMessage(sessionId, {
                type: 'chat_complete',
                skills: skills || []
              });
              session.isStreaming = false;
              session.pendingMessages = [];

              // Start the extended 10-minute workflow
              await this.startExtendedWorkflow(sessionId);
            }
          }, 100);
        }
      }, cumulativeDelay);

      session.timeouts.push(messageTimeout);
    });
  }

  private clearTimeouts(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.timeouts.forEach(timeout => clearTimeout(timeout));
    session.timeouts = [];

    // Clear extended workflow timers
    if (session.extendedWorkflowTimer) {
      clearTimeout(session.extendedWorkflowTimer);
      session.extendedWorkflowTimer = undefined;
    }

    session.extendedWorkflowCharacterTimers.forEach(timeout => clearTimeout(timeout));
    session.extendedWorkflowCharacterTimers = [];
    session.extendedWorkflowActive = false;
  }

  private sendMessage(sessionId: string, message: any): void {
    const session = this.sessions.get(sessionId);
    if (!session || session.ws.readyState !== WebSocket.OPEN) return;

    session.ws.send(JSON.stringify(message));
  }

  private sendError(sessionId: string, error: string, details?: string): void {
    this.sendMessage(sessionId, {
      type: 'error',
      error,
      details
    });
  }

  private cleanupSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      this.clearTimeouts(sessionId);

      // Clear polling interval
      if (session.pollInterval) {
        clearInterval(session.pollInterval);
      }

      // Clear autonomous timer
      if (session.autonomousTimer) {
        clearTimeout(session.autonomousTimer);
      }

      this.sessions.delete(sessionId);
    }
  }

  getSessionCount(): number {
    return this.sessions.size;
  }

  // Add a method to poll for and handle delayed character thoughts
  async pollForDelayedThoughts(sessionId: string) {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId) return;

    // Clear any existing polling interval to avoid duplicates
    if (session.pollInterval) {
      clearInterval(session.pollInterval);
    }

    // Set up polling interval (every 2 seconds for more responsive queue processing)
    const pollInterval = setInterval(async () => {
      try {
        // Only check if the session is still active
        if (!session || !session.ws || session.ws.readyState !== WebSocket.OPEN) {
          clearInterval(pollInterval);
          return;
        }

        // Skip polling if currently streaming
        if (session.isStreaming) {
          return;
        }

        // Get messages that are ready to be sent from the queue
        const readyMessages = await MessageQueueService.getReadyMessages(session.conversationId!);

        DBOS.logger.info(`Polling for ready messages - Conversation: ${session.conversationId}, Found: ${readyMessages.length} messages`);

        if (readyMessages.length > 0) {
          DBOS.logger.info(`Ready messages found: ${readyMessages.map(m => `${m.character}: ${m.text.substring(0, 50)}...`).join(', ')}`);

          // Mark messages as processing to prevent duplicate delivery
          for (const message of readyMessages) {
            await MessageQueueService.updateMessageStatus(message.id, 'PROCESSING');
          }

          // If multiple messages are ready simultaneously, reframe later ones
          if (readyMessages.length > 1) {
            await this.reframeQueuedMessages(sessionId, readyMessages);
          }

          // Send a notification that new thoughts are available
          this.sendMessage(sessionId, {
            type: 'delayed_thoughts_available',
            count: readyMessages.length
          });

          // Stream the messages with proper cumulative delays
          // Sort messages by their priority and scheduled time
          const sortedMessages = [...readyMessages].sort((a, b) => {
            if (a.priority !== b.priority) return a.priority - b.priority;
            return new Date(a.scheduled_at!).getTime() - new Date(b.scheduled_at!).getTime();
          });

          let cumulativeDelay = 0;
          const TYPING_LEAD_TIME = 3000; // 3 seconds before message
          const INITIAL_DELAY = 1000; // Initial delay before first message

          for (let i = 0; i < sortedMessages.length; i++) {
            const message = sortedMessages[i];

            // For the first message, use initial delay
            // For subsequent messages, use a base delay plus some spacing
            if (i === 0) {
              cumulativeDelay += INITIAL_DELAY;
            } else {
              // Add spacing between messages (minimum 2 seconds)
              cumulativeDelay += Math.max(2000, message.delay_ms / 2);
            }

            // Schedule typing indicator - ensure it's always at least a few seconds from now
            const typingDelay = Math.max(500, cumulativeDelay - TYPING_LEAD_TIME);
            setTimeout(() => {
              if (session && session.ws && session.ws.readyState === WebSocket.OPEN && !session.isStreaming) {
                this.sendMessage(sessionId, {
                  type: 'typing_start',
                  character: message.character
                });
              }
            }, typingDelay);

            // Schedule message
            setTimeout(async () => {
              if (session && session.ws && session.ws.readyState === WebSocket.OPEN && !session.isStreaming) {
                // Stop typing indicator
                this.sendMessage(sessionId, {
                  type: 'typing_stop',
                  character: message.character
                });

                // Send the message
                this.sendMessage(sessionId, {
                  type: 'delayed_thought',
                  character: message.character,
                  text: message.text
                });

                // Mark message as sent and add to conversation history (if not already there)
                await MessageQueueService.updateMessageStatus(message.id, 'SENT');

                // Check if this message already exists in conversation history to prevent duplicates
                const messageExists = await ConversationService.checkMessageExists(
                  message.character,
                  message.text,
                  message.conversation_id
                );

                if (!messageExists) {
                  await ConversationService.addMessage(message.character, message.text, message.conversation_id);
                  DBOS.logger.info(`Added queued message to conversation history: ${message.character}: ${message.text.substring(0, 50)}...`);
                } else {
                  DBOS.logger.info(`Skipped adding queued message (already in conversation history): ${message.character}: ${message.text.substring(0, 50)}...`);
                }
              }
            }, cumulativeDelay);
          }
        }
      } catch (error) {
        DBOS.logger.error(`Error polling for ready messages: ${(error as Error).message}`);
      }
    }, 2000);

    // Store the interval ID so we can clear it when the session ends
    session.pollInterval = pollInterval;
  }

  // Cancel pending character thoughts when user interrupts
  private async cancelPendingCharacterThoughts(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId) return;

    try {
      // Get any pending messages from the queue
      const pendingMessages = await MessageQueueService.getPendingMessages(session.conversationId);

      if (pendingMessages.length > 0) {
        DBOS.logger.info(`Handling ${pendingMessages.length} pending messages due to user interruption`);

        // For each pending message, decide whether to cancel or reframe
        for (const message of pendingMessages) {
          try {
            // If the message was scheduled very soon, reframe it to acknowledge the interruption
            const timeUntilScheduled = new Date(message.scheduled_at!).getTime() - Date.now();

            if (timeUntilScheduled < 10000) { // Less than 10 seconds away
              const reframingPrompt = `The user just sent a new message, interrupting the conversation.

Your original planned response was: "${message.text}"
User's new message: "${session.lastUserMessage || 'new input'}"

Generate a brief response that acknowledges the user's new input instead. Keep your character voice but pivot to address what they just said. Respond with JSON in this format:
{
  "reply": [{"character": "${message.character}", "text": "your new response", "delay": ${message.delay_ms}}],
  "skills": [],
  "theme": "interruption response"
}`;

              const systemPrompt = await PromptService.getSystemPrompt(`${message.character.toLowerCase()}_system`);
              const llmResponse = await this.chatService.getLLMService().generate(systemPrompt, reframingPrompt);

              if (llmResponse && llmResponse.reply && llmResponse.reply.length > 0) {
                // Update the message with the new response
                await MessageQueueService.updateMessageText(message.id, llmResponse.reply[0].text);
                DBOS.logger.info(`Reframed ${message.character}'s queued message due to interruption`);
              }
            } else {
              // Cancel messages that were scheduled further in the future
              await MessageQueueService.updateMessageStatus(message.id, 'CANCELLED');
              DBOS.logger.info(`Cancelled ${message.character}'s queued message due to interruption`);
            }
          } catch (error) {
            DBOS.logger.error(`Error handling ${message.character}'s queued message: ${(error as Error).message}`);
          }
        }
      }
    } catch (error) {
      DBOS.logger.error(`Error handling pending messages during interruption: ${(error as Error).message}`);
    }
  }

  // Reframe queued messages so later ones acknowledge earlier ones
  private async reframeQueuedMessages(sessionId: string, messages: QueuedMessage[]): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId) return;

    // Sort messages by priority and scheduled time (earliest first)
    const sortedMessages = [...messages].sort((a, b) => {
      if (a.priority !== b.priority) return a.priority - b.priority;
      return new Date(a.scheduled_at!).getTime() - new Date(b.scheduled_at!).getTime();
    });

    // For messages after the first one, reframe them to acknowledge previous messages
    for (let i = 1; i < sortedMessages.length; i++) {
      const currentMessage = sortedMessages[i];
      const previousMessages = sortedMessages.slice(0, i);

      try {
        // Build context with previous messages that will appear before this one
        const precedingContext = previousMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n');

        // Get recent conversation context
        const recentMessages = await ConversationService.getDelayedThoughts(session.conversationId);
        const conversationContext = recentMessages.slice(-3).map(msg => `${msg.character}: ${msg.text}`).join('\n');

        const reframingPrompt = `Based on this conversation and the messages that will appear before yours, reframe your response to acknowledge what others have said:

Conversation context:
${conversationContext}

Messages that will appear before yours:
${precedingContext}

Your original message: "${currentMessage.text}"

Reframe your message to acknowledge the preceding messages while maintaining your character's voice. Respond with JSON in this format:
{
  "reply": [{"character": "${currentMessage.character}", "text": "your reframed message", "delay": ${currentMessage.delay_ms}}],
  "skills": [],
  "theme": "reframed response"
}`;

        const systemPrompt = await PromptService.getSystemPrompt(`${currentMessage.character.toLowerCase()}_system`);
        const llmResponse = await this.chatService.getLLMService().generate(systemPrompt, reframingPrompt);

        if (llmResponse && llmResponse.reply && llmResponse.reply.length > 0) {
          // Update the message text in the queue
          await MessageQueueService.updateMessageText(currentMessage.id, llmResponse.reply[0].text);
          DBOS.logger.info(`Reframed queued message for ${currentMessage.character}: ${llmResponse.reply[0].text.substring(0, 50)}...`);
        }
      } catch (error) {
        DBOS.logger.error(`Error reframing queued message for ${currentMessage.character}: ${(error as Error).message}`);
        // Keep original message if reframing fails
      }
    }
  }



  // Add a method to handle delayed character thoughts
  async handleDelayedCharacterThought(sessionId: string, message: DelayedMessage) {
    const session = this.sessions.get(sessionId);
    if (!session || !session.ws || session.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    // Add a longer delay for background thoughts (5-15 seconds)
    const thoughtDelay = 5000 + Math.random() * 10000;
    const TYPING_LEAD_TIME = 3000; // Show typing 3 seconds before message

    // Schedule typing indicator
    const typingDelay = Math.max(0, thoughtDelay - TYPING_LEAD_TIME);
    setTimeout(() => {
      if (session && !session.isStreaming && session.ws && session.ws.readyState === WebSocket.OPEN) {
        this.sendMessage(sessionId, {
          type: 'typing_start',
          character: message.character
        });
      }
    }, typingDelay);

    // Schedule the actual message
    setTimeout(() => {
      // Only send if the session is still active and not in the middle of streaming
      if (session && !session.isStreaming && session.ws && session.ws.readyState === WebSocket.OPEN) {
        // Stop typing indicator
        this.sendMessage(sessionId, {
          type: 'typing_stop',
          character: message.character
        });

        // Send the message
        this.sendMessage(sessionId, {
          type: 'delayed_thought',
          character: message.character,
          text: message.text
        });
      }
    }, thoughtDelay);
  }

  // Update the startChat method to initialize polling for delayed thoughts
  async startChat(sessionId: string, userMessage: string, conversationId?: number): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    // Set the conversation ID if provided
    if (conversationId) {
      session.conversationId = conversationId;
    }

    try {
      // Use the ChatService to process the message
      const result = await this.chatService.processUserMessage({ text: userMessage });
      const response = result.response;

      if (!response) {
        this.sendError(sessionId, 'No response from chat service');
        return;
      }

      // Set the conversation ID from the result
      session.conversationId = result.conversationId;
      this.pollForDelayedThoughts(sessionId);

      if (response.reply && Array.isArray(response.reply)) {
        this.sendMessage(sessionId, {
          type: 'chat_start',
          theme: response.theme || 'Chat Response'
        });

        await this.streamDelayedMessages(sessionId, response.reply, response.skills);
      } else {
        this.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.sendError(sessionId, 'Failed to process chat message', (error as Error).message);
    }
  }

  // Update the closeSession method to clean up polling interval
  closeSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      // Clear all timeouts
      for (const timeout of session.timeouts) {
        clearTimeout(timeout);
      }
      
      // Clear polling interval if it exists
      if (session.pollInterval) {
        clearInterval(session.pollInterval);
      }
      
      // Close WebSocket if it's open
      if (session.ws && session.ws.readyState === WebSocket.OPEN) {
        session.ws.close();
      }
      
      this.sessions.delete(sessionId);
    }
  }

  private scheduleAutonomousInteraction(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId) return;
  
    // Clear any existing autonomous interaction timer
    if (session.autonomousTimer) {
      clearTimeout(session.autonomousTimer);
    }
  
    // Set a timer for autonomous interaction (e.g., 30 seconds of inactivity)
    session.autonomousTimer = setTimeout(async () => {
      // Only proceed if the session is still active and not streaming
      if (!session || !session.ws || session.ws.readyState !== WebSocket.OPEN || session.isStreaming) {
        return;
      }
      
      try {
        // Get recent messages to use as context
        const recentMessages = await ConversationService.getDelayedThoughts(session.conversationId!);
        if (recentMessages.length === 0) return;
        
        // Choose a random character to continue the conversation
        const characters = ['Fora', 'Jan', 'Lou'];
        const character = characters[Math.floor(Math.random() * characters.length)];
        
        // Build context from recent messages
        const context = recentMessages.slice(-5).map(msg => `${msg.character}: ${msg.text}`).join('\n');
        
        // Generate an autonomous thought
        const handle = await DBOS.startWorkflow(ForaChat).characterThoughtWorkflow(
          session.conversationId!,
          context,
          character
        );
        
        const result = await handle.getResult();
        if (result && result.text) {
          // Send the autonomous message
          this.sendMessage(sessionId, {
            type: 'autonomous_message',
            character: result.character,
            text: result.text
          });
          
          // Schedule another autonomous interaction
          this.scheduleAutonomousInteraction(sessionId);
        }
      } catch (error) {
        console.error('Error in autonomous interaction:', error);
      }
    }, 30000); // 30 seconds of inactivity
  }

  // Start the extended 10-minute workflow where characters continue to chime in
  private async startExtendedWorkflow(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId) return;

    // Allow extended workflow for all conversation types, including general greetings
    // This ensures follow-up character thoughts are delivered to the client

    // Clear any existing autonomous timer since we're starting extended workflow
    if (session.autonomousTimer) {
      clearTimeout(session.autonomousTimer);
      session.autonomousTimer = undefined;
    }

    session.extendedWorkflowActive = true;
    session.extendedWorkflowStartTime = Date.now();

    // Notify the client that extended workflow has started
    this.sendMessage(sessionId, {
      type: 'extended_workflow_start',
      duration: 600000, // 10 minutes in milliseconds
      message: 'Characters will continue the conversation for the next 10 minutes...'
    });

    // Set the main timer for 10 minutes
    session.extendedWorkflowTimer = setTimeout(() => {
      this.endExtendedWorkflow(sessionId);
    }, 600000); // 10 minutes

    // Schedule the first character interaction
    this.scheduleNextCharacterInteraction(sessionId);
  }

  // Schedule the next character interaction during extended workflow
  private scheduleNextCharacterInteraction(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session || !session.extendedWorkflowActive || !session.conversationId) return;

    // Random delay between 15-45 seconds for varied pacing
    const delay = 15000 + Math.random() * 30000;

    const timer = setTimeout(async () => {
      // Check if session is still active and extended workflow is running
      if (!session || !session.extendedWorkflowActive || !session.ws ||
          session.ws.readyState !== WebSocket.OPEN || session.isStreaming) {
        return;
      }

      try {
        // Get recent messages for context
        const recentMessages = await ConversationService.getDelayedThoughts(session.conversationId!);
        if (recentMessages.length === 0) {
          // Schedule next interaction even if no messages
          this.scheduleNextCharacterInteraction(sessionId);
          return;
        }

        // Choose a random character
        const characters = ['Fora', 'Jan', 'Lou'];
        const character = characters[Math.floor(Math.random() * characters.length)];

        // Build context from recent messages
        const context = recentMessages.slice(-5).map(msg => `${msg.character}: ${msg.text}`).join('\n');

        // Generate character thought
        const handle = await DBOS.startWorkflow(ForaChat).characterThoughtWorkflow(
          session.conversationId!,
          context,
          character
        );

        const result = await handle.getResult();
        if (result && result.text) {
          // Show typing indicator first
          this.sendMessage(sessionId, {
            type: 'typing_start',
            character: result.character
          });

          // Wait 3 seconds, then send the message
          setTimeout(() => {
            if (session && session.extendedWorkflowActive && session.ws && session.ws.readyState === WebSocket.OPEN) {
              // Stop typing indicator
              this.sendMessage(sessionId, {
                type: 'typing_stop',
                character: result.character
              });

              // Send the extended workflow message
              this.sendMessage(sessionId, {
                type: 'extended_workflow_message',
                character: result.character,
                text: result.text,
                timeRemaining: this.getExtendedWorkflowTimeRemaining(sessionId)
              });
            }
          }, 3000);

          // Schedule the next interaction
          this.scheduleNextCharacterInteraction(sessionId);
        } else {
          // If no result, still schedule next interaction
          this.scheduleNextCharacterInteraction(sessionId);
        }
      } catch (error) {
        console.error('Error in extended workflow character interaction:', error);
        // Schedule next interaction even on error
        this.scheduleNextCharacterInteraction(sessionId);
      }
    }, delay);

    session.extendedWorkflowCharacterTimers.push(timer);
  }

  // End the extended workflow
  private endExtendedWorkflow(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.extendedWorkflowActive = false;

    // Clear all extended workflow timers
    if (session.extendedWorkflowTimer) {
      clearTimeout(session.extendedWorkflowTimer);
      session.extendedWorkflowTimer = undefined;
    }

    session.extendedWorkflowCharacterTimers.forEach(timer => clearTimeout(timer));
    session.extendedWorkflowCharacterTimers = [];

    // Notify the client that extended workflow has ended
    this.sendMessage(sessionId, {
      type: 'extended_workflow_end',
      message: 'Extended conversation period has ended.'
    });

    // Resume normal autonomous interaction scheduling
    this.scheduleAutonomousInteraction(sessionId);
  }

  // Get remaining time in extended workflow
  private getExtendedWorkflowTimeRemaining(sessionId: string): number {
    const session = this.sessions.get(sessionId);
    if (!session || !session.extendedWorkflowActive || !session.extendedWorkflowStartTime) {
      return 0;
    }

    const elapsed = Date.now() - session.extendedWorkflowStartTime;
    const remaining = Math.max(0, 600000 - elapsed); // 10 minutes - elapsed
    return remaining;
  }

  // Extend the workflow timeout when user is active
  private extendWorkflowTimeout(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session || !session.extendedWorkflowActive) return;

    // Clear the existing main timer
    if (session.extendedWorkflowTimer) {
      clearTimeout(session.extendedWorkflowTimer);
    }

    // Reset the start time to now (effectively extending by 10 minutes from now)
    session.extendedWorkflowStartTime = Date.now();

    // Set a new 10-minute timer
    session.extendedWorkflowTimer = setTimeout(() => {
      this.endExtendedWorkflow(sessionId);
    }, 600000); // 10 minutes

    // Notify the client that the workflow has been extended
    this.sendMessage(sessionId, {
      type: 'extended_workflow_extended',
      message: 'Extended conversation period renewed for another 10 minutes due to your activity!',
      newDuration: 600000,
      timeRemaining: 600000
    });
  }

  // Public method to manually start extended workflow (for testing or manual triggers)
  public async triggerExtendedWorkflow(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId || session.extendedWorkflowActive) {
      return false;
    }

    await this.startExtendedWorkflow(sessionId);
    return true;
  }

  // Public method to get extended workflow status
  public getExtendedWorkflowStatus(sessionId: string): {
    active: boolean;
    timeRemaining: number;
    startTime?: number;
  } {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return { active: false, timeRemaining: 0 };
    }

    return {
      active: session.extendedWorkflowActive,
      timeRemaining: this.getExtendedWorkflowTimeRemaining(sessionId),
      startTime: session.extendedWorkflowStartTime
    };
  }
}
